import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { useState } from 'react';
import { InstructionLink } from './InstructionLink';
import { ReadMoreButton } from './ReadMoreButton';
import { useExpandableContent } from '../../../../hooks/useExpandableContent';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const { editorState, link: instructionsLink, label: instructionsText, size, defaultHeight = 150 } = props;

  const [isHidden, setIsHidden] = useState(true);
  const toggleExpansion = () => setIsHidden((prev) => !prev);

  const { isTruncated, height, className } = useExpandableContent({
    selector: '.instruction-text',
    defaultHeight,
    isHidden,
  });

  return (
    <>
      <div className={`instruction-text ${className}`.trim()} style={{ height }}>
        <ReadonlyRichTextEditor editorState={editorState} />
        <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
      </div>
      {isTruncated && (
        <ReadMoreButton
          isHidden={isHidden}
          onToggle={toggleExpansion}
          className={!isHidden && instructionsLink ? 'mt-2' : ''}
        />
      )}
    </>
  );
};

import { useEffect, useState } from 'react';
import { QuestionProps } from '../questionInterfaces';
import { GlossaryText } from '../../../glossary-text/GlossaryText';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { InstructionLink } from './InstructionLink';
import { getInstructionLabel } from '@utils/universalTracker';
import { useExpandableContent } from './useExpandableContent';
import { ReadMoreButton } from './ReadMoreButton';

const defaultHeight = 90;

interface Props extends Pick<QuestionProps, 'utr' | 'glossaryState' | 'alternativeCode'> {
  selectedLanguage?: string;
  startAltTextAnimation: boolean;
  instructionsLinkSize?: string;
}

const SingleInstructions = (props: Props & { isHidden: boolean, children: JSX.Element }) => {
  const { utr, glossaryState, alternativeCode, isHidden, instructionsLinkSize } = props;

  const { isTruncated, height, className } = useExpandableContent({
    selector: '.instruction-text',
    defaultHeight,
    isHidden
  });

  if (!utr) {
    return <LoadingPlaceholder height={40} />;
  }

  const instructionsText = getInstructionLabel(alternativeCode);
  const instructionsLink = utr.getInstructionsLink(alternativeCode);
  const instructions: string = utr.getInstructions(alternativeCode);

  return (
    <>
      <div className={`instruction-text ${className}`} style={{ height }}>
        <GlossaryText text={instructions} glossary={glossaryState} />
        <InstructionLink link={instructionsLink} label={instructionsText} size={instructionsLinkSize} />
      </div>
      {isTruncated ? props.children : null}
    </>
  );
}

export const Instructions = (props: Props) => {
  const { utr, alternativeCode, selectedLanguage, startAltTextAnimation } = props;

  const [isHidden, setIsHidden] = useState(true);
  const toggleHiding = () => setIsHidden((prev) => !prev);

  const showInstructionLink = !isHidden && utr?.getInstructionsLink(alternativeCode);

  const HiddenBtn = () => (
    <ReadMoreButton
      isHidden={isHidden}
      onToggle={toggleHiding}
      variant='caret'
      className={showInstructionLink ? 'd-block mt-2' : ''}
    />
  );

  return (
    <div className='animated-container'>
      <div className={`text-animated ${startAltTextAnimation ? 'disappear-right' : ''}`}>
        <SingleInstructions {...props} alternativeCode={alternativeCode} isHidden={isHidden}>
          <HiddenBtn />
        </SingleInstructions>
      </div>
      <div className={`text-animated ${startAltTextAnimation ? '' : 'disappear-left'}`}>
        <SingleInstructions {...props} alternativeCode={selectedLanguage ?? alternativeCode} isHidden={isHidden}>
          <HiddenBtn />
        </SingleInstructions>
      </div>
    </div>
  );
};

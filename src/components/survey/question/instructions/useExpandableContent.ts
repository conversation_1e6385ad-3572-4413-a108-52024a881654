/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useState } from 'react';

interface UseExpandableContentProps {
  selector: string;
  defaultHeight?: number;
  isHidden?: boolean;
}

interface UseExpandableContentReturn {
  isHidden: boolean;
  toggleExpansion: () => void;
  isTruncated: boolean;
  height: string;
  className: string;
}

/**
 * Hook for expandable content functionality using DOM query selectors
 * Handles height measurement, state management, and styling for read more/less components
 */
export const useExpandableContent = ({
  selector,
  defaultHeight = 150,
  isHidden: externalIsHidden
}: UseExpandableContentProps): UseExpandableContentReturn => {
  const [internalIsHidden, setInternalIsHidden] = useState(true);

  // Use external state if provided, otherwise use internal state
  const isHidden = externalIsHidden !== undefined ? externalIsHidden : internalIsHidden;
  const setHidden = externalIsHidden !== undefined ? () => {} : setInternalIsHidden;

  const element = document.querySelector(selector);
  const textHeight = element?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = isTruncated && isHidden ? `${defaultHeight}px` :
                !isTruncated ? '100%' :
                `${textHeight}px`;

  const className = isTruncated ?
    `is-truncated ${isHidden ? 'collapsed' : 'expanded'}` :
    '';

  const toggleExpansion = () => setHidden(prev => !prev);

  return {
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className
  };
};

interface UseExpandableContentProps {
  selector: string;
  defaultHeight?: number;
  isHidden: boolean;
}

export const useExpandableContent = ({ selector, defaultHeight = 150, isHidden }: UseExpandableContentProps) => {
  const element = document.querySelector(selector);
  const textHeight = element?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = isTruncated && isHidden ? `${defaultHeight}px` : !isTruncated ? '100%' : `${textHeight}px`;

  const className = isTruncated ? `is-truncated ${isHidden ? 'collapsed' : 'expanded'}` : '';

  return {
    isTruncated,
    height,
    className,
  };
};

import { Button } from 'reactstrap';

interface ReadMoreButtonProps {
  isHidden: boolean;
  onToggle: () => void;
  className?: string;
}

export const ReadMoreButton = ({ isHidden, onToggle, className = '' }: ReadMoreButtonProps) => {
  const iconClass = `fal fa-caret-${isHidden ? 'down' : 'up'}`;

  const text = isHidden ? 'Read more' : 'Read less';

  return (
    <Button color='link-secondary' onClick={onToggle} className={`text-xs ${className}`}>
      <div className='text-sm py-1'>
        <i className={`${iconClass} mr-1 text-sm`} />
        {text}
      </div>
    </Button>
  );
};

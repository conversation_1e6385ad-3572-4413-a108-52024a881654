/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ReadonlyInstructionsEditor } from './ReadonlyInstructionsEditor';
import { createEditorState } from '@fixtures/editor-state-fixture';

describe('ReadonlyInstructionsEditor', () => {
  it('renders instruction link when provided', () => {
    const editorState = createEditorState('Some instruction text');

    const { container } = render(
      <ReadonlyInstructionsEditor editorState={editorState} link='https://example.com' label='View Instructions' />,
    );

    expect(container.querySelector('.instruction-text')).toBeInTheDocument();
    expect(screen.getByText('Some instruction text')).toBeInTheDocument();
    expect(screen.getByText('View Instructions')).toBeInTheDocument();
  });

  it('does not render instruction link when not provided', () => {
    const editorState = createEditorState('Some instruction text');

    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('renders without read more/less buttons initially', () => {
    const editorState = createEditorState('Short text');
    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    expect(screen.queryByText('Read less')).not.toBeInTheDocument();
  });
});

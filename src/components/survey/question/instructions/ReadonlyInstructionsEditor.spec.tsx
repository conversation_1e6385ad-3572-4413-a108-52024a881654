/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReadonlyInstructionsEditor } from './ReadonlyInstructionsEditor';
import { createEditorState } from '@fixtures/editor-state-fixture';

describe('ReadonlyInstructionsEditor', () => {
  it('renders instruction link when provided', () => {
    const editorState = createEditorState('Some instruction text');

    const { container } = render(
      <ReadonlyInstructionsEditor editorState={editorState} link='https://example.com' label='View Instructions' />,
    );

    expect(container.querySelector('.instruction-text')).toBeInTheDocument();
    expect(screen.getByText('Some instruction text')).toBeInTheDocument();
    expect(screen.getByText('View Instructions')).toBeInTheDocument();
  });

  it('does not render instruction link when not provided', () => {
    const editorState = createEditorState('Some instruction text');

    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  describe('Read more/less functionality', () => {
    beforeEach(() => {
      // Mock querySelector to return an element with large scrollHeight to trigger truncation
      const mockElement = {
        scrollHeight: 300, // Large content that exceeds default height of 150px
      };

      vi.spyOn(document, 'querySelector').mockImplementation((selector) => {
        if (selector === '.instruction-text') {
          return mockElement as any;
        }
        return null;
      });
    });

    it('toggles to read less when read more button is clicked', async () => {
      const user = userEvent.setup();
      const editorState = createEditorState('Very long instruction text that should be truncated');

      render(<ReadonlyInstructionsEditor editorState={editorState} />);

      const readMoreButton = screen.getByText('Read more');
      expect(readMoreButton).toBeInTheDocument();
      expect(screen.queryByText('Read less')).not.toBeInTheDocument();

      await user.click(readMoreButton);

      expect(screen.getByText('Read less')).toBeInTheDocument();
      expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    });

    it('toggles back to read more when read less button is clicked', async () => {
      const user = userEvent.setup();
      const editorState = createEditorState('Very long instruction text that should be truncated');

      render(<ReadonlyInstructionsEditor editorState={editorState} />);

      // First click to expand
      const readMoreButton = screen.getByText('Read more');
      await user.click(readMoreButton);

      // Then click to collapse
      const readLessButton = screen.getByText('Read less');
      await user.click(readLessButton);

      expect(screen.getByText('Read more')).toBeInTheDocument();
      expect(screen.queryByText('Read less')).not.toBeInTheDocument();
    });

    it('applies correct CSS classes when content is expanded', async () => {
      const user = userEvent.setup();
      const editorState = createEditorState('Very long instruction text that should be truncated');

      const { container } = render(<ReadonlyInstructionsEditor editorState={editorState} />);

      const readMoreButton = screen.getByText('Read more');
      await user.click(readMoreButton);

      const instructionText = container.querySelector('.instruction-text');
      expect(instructionText).toHaveClass('is-truncated');
      expect(instructionText).toHaveClass('expanded');
      expect(instructionText).not.toHaveClass('collapsed');
      expect(instructionText).toHaveStyle({ height: '300px' }); // scrollHeight from mock
    });

    it('sets correct height style when content is collapsed', () => {
      const editorState = createEditorState('Very long instruction text that should be truncated');

      const { container } = render(<ReadonlyInstructionsEditor editorState={editorState} />);

      const instructionText = container.querySelector('.instruction-text');
      expect(instructionText).toHaveClass('is-truncated');
      expect(instructionText).toHaveClass('collapsed');
      expect(instructionText).toHaveStyle({ height: '150px' });
    });
  });
});
